# Render Redirects for FactCheck App

# Redirect all API calls to API Gateway
# Note: These URLs will be replaced during build process with actual environment variables
/api/* __API_GATEWAY_URL__/api/:splat 200
/auth/* __API_GATEWAY_URL__/auth/:splat 200
/users/* __API_GATEWAY_URL__/users/:splat 200
/chat/* __API_GATEWAY_URL__/chat/:splat 200
/news/* __API_GATEWAY_URL__/news/:splat 200
/links/* __API_GATEWAY_URL__/links/:splat 200
/admin/* __API_GATEWAY_URL__/admin/:splat 200
/community/* __API_GATEWAY_URL__/community/:splat 200
/posts/* __API_GATEWAY_URL__/posts/:splat 200
/votes/* __API_GATEWAY_URL__/votes/:splat 200
/comments/* https://factcheck-api-gateway.onrender.com/comments/:splat 200

# SPA fallback - all other routes serve index.html
/* /index.html 200
