const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');
const fs = require('fs');

// Load environment variables from root directory with fallback
const rootEnvPath = path.join(__dirname, '../../.env');

// Try to load from root first, fallback to local if not found
if (fs.existsSync(rootEnvPath)) {
  require('dotenv').config({ path: rootEnvPath });
} else {
  // Fallback for production environments (Ren<PERSON>, Docker)
  require('dotenv').config();
}

// Simple logger implementation
const logger = {
  info: (message, meta = {}) => console.log(`[INFO] ${message}`, meta),
  error: (message, meta = {}) => console.error(`[ERROR] ${message}`, meta),
  warn: (message, meta = {}) => console.warn(`[WARN] ${message}`, meta),
  logRequest: (req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
  }
};

const app = express();
const PORT = process.env.PORT || 8081;
const SERVICE_NAME = 'api-gateway';

// Service configuration
const services = {
  'auth-service': process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  'link-service': process.env.LINK_SERVICE_URL || 'http://localhost:3002',
  'community-service': process.env.COMMUNITY_SERVICE_URL || 'http://localhost:3003',
  'chat-service': process.env.CHAT_SERVICE_URL || 'http://localhost:3004',
  'news-service': process.env.NEWS_SERVICE_URL || 'http://localhost:3005',
  'admin-service': process.env.ADMIN_SERVICE_URL || 'http://localhost:3006'
};

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
    },
  },
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'https://frontend-eklp.onrender.com',
      'http://localhost:3000',
      'http://localhost:8080'
    ];
    
    // Clean up whitespace from origins
    const cleanOrigins = allowedOrigins.map(o => o.trim());
    
    if (cleanOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      logger.warn(`CORS blocked origin: ${origin}`, { allowedOrigins: cleanOrigins });
      callback(null, true); // Allow for now to debug
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'x-correlation-id', 
    'X-Request-ID', 
    'Cache-Control',
    'Accept',
    'Origin',
    'X-Requested-With'
  ],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Global rate limiting
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests from this IP',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(globalLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(logger.logRequest);
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}));

// Health check endpoints
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: SERVICE_NAME,
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Info endpoint
app.get('/info', (req, res) => {
  res.json({
    service: SERVICE_NAME,
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    port: PORT,
    corsOrigins: process.env.ALLOWED_ORIGINS || 'not-set',
    services
  });
});

// Test CORS endpoint
app.get('/test-cors', (req, res) => {
  res.json({
    message: 'CORS test successful',
    origin: req.headers.origin,
    timestamp: new Date().toISOString()
  });
});

// Mock endpoints for testing
app.get('/community/posts', (req, res) => {
  res.json({
    success: true,
    data: {
      posts: [
        {
          id: '1',
          title: 'Test Post from API Gateway',
          content: 'This proves the API Gateway is working',
          author: 'API Gateway',
          timestamp: new Date().toISOString()
        }
      ]
    },
    message: 'Mock data from API Gateway'
  });
});

app.get('/news/latest', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Test News from API Gateway',
        description: 'This proves the API Gateway news endpoint is working',
        source: 'API Gateway',
        timestamp: new Date().toISOString()
      }
    ],
    message: 'Mock news data from API Gateway'
  });
});

// API routes with /api prefix - Authentication
app.use('/api/users', createProxyMiddleware({
  target: services['auth-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/users': '/users' },
  onError: (err, req, res) => {
    logger.error('Auth service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Auth service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/api/auth', createProxyMiddleware({
  target: services['auth-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/auth': '/auth' },
  onError: (err, req, res) => {
    logger.error('Auth service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Auth service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Links service
app.use('/api/links', createProxyMiddleware({
  target: services['link-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/links': '/links' },
  onError: (err, req, res) => {
    logger.error('Link service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Link service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Votes routes (proxy to community service) - CRITICAL FIX
app.use('/api/votes', createProxyMiddleware({
  target: services['community-service'],
  changeOrigin: true,
  timeout: 60000, // Increased to 60 seconds for batch operations
  proxyTimeout: 60000,
  pathRewrite: { '^/api/votes': '/votes' },
  onProxyReq: (proxyReq, req, res) => {
    logger.info('Proxying votes request', {
      method: req.method,
      url: req.url,
      target: `${services['community-service']}${proxyReq.path}`,
      headers: req.headers
    });

    // Set connection keep-alive to prevent ECONNRESET
    proxyReq.setHeader('Connection', 'keep-alive');
    proxyReq.setHeader('Keep-Alive', 'timeout=60, max=1000');

    // Forward authentication headers
    if (req.headers.authorization) {
      proxyReq.setHeader('Authorization', req.headers.authorization);
    }

    // Handle JSON body for POST/PUT requests
    if (req.body && (req.method === 'POST' || req.method === 'PUT')) {
      const bodyData = JSON.stringify(req.body);
      proxyReq.setHeader('Content-Type', 'application/json');
      proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
      proxyReq.write(bodyData);
    }
  },
  onProxyRes: (proxyRes, req, res) => {
    logger.info('Votes proxy response', {
      status: proxyRes.statusCode,
      url: req.url,
      headers: proxyRes.headers
    });
  },
  onError: (err, req, res) => {
    logger.error('Community service proxy error (votes)', {
      error: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method
    });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Community service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Posts routes (proxy to community service)
app.use('/api/posts', createProxyMiddleware({
  target: services['community-service'],
  changeOrigin: true,
  timeout: 30000,
  proxyTimeout: 30000,
  pathRewrite: { '^/api/posts': '/posts' },
  onProxyReq: (proxyReq, req, res) => {
    logger.info('Proxying posts request', {
      method: req.method,
      url: req.url,
      target: services['community-service']
    });
  },
  onProxyRes: (proxyRes, req, res) => {
    logger.info('Posts proxy response', {
      method: req.method,
      status: proxyRes.statusCode,
      url: req.url
    });
  },
  onError: (err, req, res) => {
    logger.error('Community service proxy error (posts)', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Community service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Comments routes (proxy to community service)
app.use('/api/comments', createProxyMiddleware({
  target: services['community-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/comments': '/comments' },
  onError: (err, req, res) => {
    logger.error('Community service proxy error (comments)', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Community service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// General community routes (proxy to community service)
app.use('/api/community', createProxyMiddleware({
  target: services['community-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/community': '' },
  onError: (err, req, res) => {
    logger.error('Community service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Community service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Chat service
app.use('/api/chat', createProxyMiddleware({
  target: services['chat-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/chat': '/chat' },
  onError: (err, req, res) => {
    logger.error('Chat service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Chat service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// News service
app.use('/api/news', createProxyMiddleware({
  target: services['news-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/news': '/news' },
  onError: (err, req, res) => {
    logger.error('News service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'News service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Admin service
app.use('/api/admin', createProxyMiddleware({
  target: services['admin-service'],
  changeOrigin: true,
  pathRewrite: { '^/api/admin': '/admin' },
  onError: (err, req, res) => {
    logger.error('Admin service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Admin service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// Backward compatibility routes (without /api prefix)
app.use('/community', createProxyMiddleware({
  target: services['community-service'],
  changeOrigin: true,
  pathRewrite: { '^/community': '' },
  onError: (err, req, res) => {
    logger.error('Community service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Community service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/auth', createProxyMiddleware({
  target: services['auth-service'],
  changeOrigin: true,
  pathRewrite: { '^/auth': '/auth' },
  onError: (err, req, res) => {
    logger.error('Auth service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Auth service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/users', createProxyMiddleware({
  target: services['auth-service'],
  changeOrigin: true,
  pathRewrite: { '^/users': '/users' },
  onError: (err, req, res) => {
    logger.error('Auth service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Auth service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/links', createProxyMiddleware({
  target: services['link-service'],
  changeOrigin: true,
  pathRewrite: { '^/links': '/links' },
  onError: (err, req, res) => {
    logger.error('Link service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Link service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/chat', createProxyMiddleware({
  target: services['chat-service'],
  changeOrigin: true,
  pathRewrite: { '^/chat': '/chat' },
  onError: (err, req, res) => {
    logger.error('Chat service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Chat service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

app.use('/news', createProxyMiddleware({
  target: services['news-service'],
  changeOrigin: true,
  pathRewrite: { '^/news': '/news' },
  onError: (err, req, res) => {
    logger.error('News service proxy error', { error: err.message });
    if (!res.headersSent) {
      res.status(503).json({
        error: 'News service unavailable',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  }
}));

// 404 handler
app.use('*', (req, res) => {
  logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    error: 'Route not found',
    method: req.method,
    path: req.originalUrl,
    timestamp: new Date().toISOString(),
    availableRoutes: [
      'GET /health',
      'GET /info',
      'GET /test-cors',
      'GET /community/posts',
      'GET /news/latest',
      'GET /api/votes/:linkId/stats',
      'GET /api/votes/:linkId/user',
      'POST /api/votes/batch/stats',
      'POST /api/votes/batch/user',
      'POST /api/votes/:linkId',
      'GET /api/posts',
      'POST /api/posts',
      'GET /api/comments',
      'POST /api/comments'
    ]
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Request error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  });

  if (!res.headersSent) {
    res.status(error.status || 500).json({
      error: error.message || 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`🚀 API Gateway started on port ${PORT}`, {
    service: SERVICE_NAME,
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    services: Object.keys(services).length
  });

  console.log(`Available endpoints:`);
  console.log(`  - GET /health`);
  console.log(`  - GET /info`);
  console.log(`  - GET /test-cors`);
  console.log(`  - GET /community/posts`);
  console.log(`  - GET /news/latest`);
  console.log(`  - /api/votes/* (proxied to community service)`);
  console.log(`  - /api/posts/* (proxied to community service)`);
  console.log(`  - /api/comments/* (proxied to community service)`);
  console.log(`  - /api/auth/* (proxied to auth service)`);
  console.log(`  - /api/users/* (proxied to auth service)`);
  console.log(`  - /api/links/* (proxied to link service)`);
  console.log(`  - /api/chat/* (proxied to chat service)`);
  console.log(`  - /api/news/* (proxied to news service)`);
  console.log(`  - /api/admin/* (proxied to admin service)`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection', { error: err.message, stack: err.stack });
  server.close(() => {
    process.exit(1);
  });
});

module.exports = app;
