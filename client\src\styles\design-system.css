/* Design System - Enterprise Level */
/* Centralized design tokens and component styles */

/* Design Tokens */
:root {
  /* Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  --color-success-500: #10b981;
  --color-warning-500: #f59e0b;
  --color-error-500: #ef4444;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Transitions */
  --transition-fast: 150ms ease-out;
  --transition-base: 200ms ease-out;
  --transition-slow: 300ms ease-out;
}

/* Dark mode overrides */
.dark {
  --color-primary-50: #1e3a8a;
  --color-primary-100: #1d4ed8;
  --color-secondary-50: #0f172a;
  --color-secondary-100: #1e293b;
}

/* Base Components */
/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  outline: none;
  border: none;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-secondary {
  background-color: rgb(75 85 99);
  color: white;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.btn-secondary:hover {
  background-color: rgb(55 65 81);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-outline {
  border: 2px solid rgb(37 99 235);
  color: rgb(37 99 235);
  background-color: transparent;
}

.btn-outline:hover {
  background-color: rgb(239 246 255);
}

.dark .btn-outline:hover {
  background-color: rgba(37, 99, 235, 0.2);
}

.btn-ghost {
  color: rgb(75 85 99);
  background-color: transparent;
}

.btn-ghost:hover {
  background-color: rgb(243 244 246);
}

.dark .btn-ghost {
  color: rgb(156 163 175);
}

.dark .btn-ghost:hover {
  background-color: rgb(31 41 55);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border: 1px solid rgb(229 231 235);
  overflow: hidden;
}

.dark .card {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
}

.card-elevated {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  transition: box-shadow 0.3s;
}

.card-elevated:hover {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

.card-interactive {
  cursor: pointer;
  transition: all 0.3s;
}

.card-interactive:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  transform: translateY(-0.25rem);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgb(229 231 235);
}

.dark .card-header {
  border-bottom-color: rgb(55 65 81);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1.5rem;
  border-top: 1px solid rgb(229 231 235);
  background-color: rgb(249 250 251);
}

.dark .card-footer {
  border-top-color: rgb(55 65 81);
  background-color: rgba(31, 41, 55, 0.5);
}

/* Forms */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55 65 81);
}

.dark .form-label {
  color: rgb(209 213 219);
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.5rem;
  background-color: white;
  color: rgb(17 24 39);
  transition: colors 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.dark .form-input {
  border-color: rgb(75 85 99);
  background-color: rgb(31 41 55);
  color: rgb(243 244 246);
}

.form-input-error {
  border-color: rgb(239 68 68);
}

.form-input-error:focus {
  border-color: rgb(239 68 68);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.form-error {
  font-size: 0.875rem;
  color: rgb(220 38 38);
}

.dark .form-error {
  color: rgb(248 113 113);
}

/* Typography */
.heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: rgb(17 24 39);
}

.dark .heading-1 {
  color: white;
}

.heading-2 {
  font-size: 1.875rem;
  font-weight: 600;
  color: rgb(31 41 55);
}

.dark .heading-2 {
  color: rgb(243 244 246);
}

/* Layout */
.container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Loading States */
.skeleton {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(229 231 235);
  border-radius: 0.25rem;
}

.dark .skeleton {
  background-color: rgb(55 65 81);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Utility Classes */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Responsive Utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 2rem;
  }
}

.responsive-grid-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.responsive-grid-2 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.responsive-grid-3 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.responsive-grid-4 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .responsive-grid-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .responsive-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Keyframes - Hardware Accelerated */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}
